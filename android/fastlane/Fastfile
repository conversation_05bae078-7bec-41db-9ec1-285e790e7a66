lane :deploy do
  gradle(task: "bundleRelease")
  supply(
    track: 'internal', # The release track (e.g., 'internal', 'alpha', 'beta', 'production')
    aab: '/Users/<USER>/Desktop/EdulystFlutterApps/lo2_app_upgrade/build/app/outputs/bundle/release/app-release.aab', # Path to the generated release AAB file
    json_key: '/Users/<USER>/Downloads/edulystventures-c17c478e8fd6.json', # Path to your Play Store service account JSON file
    package_name: 'com.singulariswow' # Package name of your Flutter app
  )
end



lane :build do
  gradle(task: "bundleRelease")
end

