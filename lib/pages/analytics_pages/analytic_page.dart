// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_mecfuture_2_0/blocs/bloc_manager.dart';
// import 'package:flutter_mecfuture_2_0/blocs/home_bloc.dart';
// import 'package:flutter_mecfuture_2_0/data/api/api_service.dart';
// import 'package:flutter_mecfuture_2_0/data/models/response/home_response/get_courses_resp.dart';
// import 'package:flutter_mecfuture_2_0/pages/analytics_pages/asking_rate_calculator_page.dart';
// import 'package:flutter_mecfuture_2_0/pages/custom_pages/common_container.dart';
// import 'package:flutter_mecfuture_2_0/pages/custom_pages/custom_widgets/next_page_routing.dart';
// import 'package:flutter_mecfuture_2_0/pages/custom_pages/custom_widgets/courses_loader.dart';
// import 'package:flutter_mecfuture_2_0/pages/notification_list_page.dart';
// import 'package:flutter_mecfuture_2_0/utils/log.dart';
// import 'package:flutter_mecfuture_2_0/utils/strings.dart';
// import 'package:flutter_mecfuture_2_0/utils/styles.dart';
// import 'package:flutter_mecfuture_2_0/utils/theme/theme_extensions.dart';
// import 'package:hive_flutter/hive_flutter.dart';
// import '../analytics_pages/my_certificates_page.dart';
// import 'kpi_wise_analysis_page.dart';
// import 'my_analitics_page.dart';
// import 'team_analysis_page.dart';

// class AnalyticPage extends StatefulWidget {
//   final bool? isViewAll;
//   final Widget? drawerWidget;

//   const AnalyticPage({super.key, this.isViewAll, this.drawerWidget});

//   @override
//   State<AnalyticPage> createState() => _AnalyticPageState();
// }

// class _AnalyticPageState extends State<AnalyticPage> {
//   final ScrollController _pageListScrollController = ScrollController();
//   List<ListElement> _getCoursesResp = [];

//   bool _isLoading = true;

//   int _selectedPage = 0;
//   List<String?> _pageList = [];

//   String? _selectedMemberId;

//   @override
//   void initState() {
//     super.initState();
//     _getCoursesListData(0);
//   }

//   @override
//   void didChangeDependencies() {
//     _pageList = [
//       Strings.of(context)?.My_Analytics,
//       Strings.of(context)?.Team_Analytics,
//       Strings.of(context)?.KPI_Analytics,
//     ];
//     super.didChangeDependencies();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: BlocManager(
//           initState: (BuildContext context) {},
//           child: BlocListener<HomeBloc, HomeState>(
//             listener: (context, state) {
//               Log.v("Loading....................GetCoursesState build");
//               if (state is GetCoursesState) _handleAnnouncmentData(state);
//             },
//             child: widget.isViewAll == true
//                 ? _verticalList()
//                 : MyAnalyticsPage(
//                     getCoursesResp: _getCoursesResp, isLoading: _isLoading),
//           )),
//     );
//   }

//   CommonContainer _verticalList() {
//     return CommonContainer(
//       isBackShow: true,
//       isDrawerEnable: true,
//       drawerWidget: widget.drawerWidget,
//       isContainerHeight: !_isLoading ? false : true,
//       isScrollable: true,
//       bgChildColor: Color(0xFFEEEEF3),
//       belowTitle: _menuItems(),
//       title: Strings.of(context)?.analytics,
//       onBackPressed: () {
//         Navigator.pop(context);
//       },
//       isNotification: true,
//       onSkipClicked: () {
//         Navigator.push(context, NextPageRoute(NotificationListPage()));
//       },
//       isLoading: false,
//       child: _mainBody(),
//     );
//   }

//   Widget _mainBody() {
//     if (_selectedPage == 0) {
//       return ValueListenableBuilder(
//         valueListenable: Hive.box("analytics").listenable(),
//         builder: (bc, Box box, child) {
//           if (box.get("myAnalytics") == null) {
//             return CoursesLoader();
//           } else if (box.get("myAnalytics").isEmpty) {
//             return SizedBox(
//               height: MediaQuery.of(context).size.height / 1.8,
//               child: Center(
//                 child: Text(
//                   'no_data',
//                   style: Styles.textBold(),
//                 ).tr(),
//               ),
//             );
//           }
//           _getCoursesResp = box
//               .get("myAnalytics")
//               .map((e) => ListElement.fromJson(Map<String, dynamic>.from(e)))
//               .cast<ListElement>()
//               .toList();
//           return MyAnalyticsPage(
//             getCoursesResp: _getCoursesResp,
//             isLoading: false,
//           );
//         },
//       );
//     }
//     if (_selectedPage == 1) {
//       return ValueListenableBuilder(
//         valueListenable: Hive.box("analytics").listenable(),
//         builder: (bc, Box box, child) {
//           if (box.get("teamAnalytics") == null) {
//             return CoursesLoader();
//           } else if (box.get("teamAnalytics").isEmpty) {
//             return SizedBox(
//               height: MediaQuery.of(context).size.height / 1.8,
//               child: Center(
//                 child: Text(
//                   'no_data',
//                   style: Styles.textBold(),
//                 ).tr(),
//               ),
//             );
//           }
//           _getCoursesResp = box
//               .get("teamAnalytics")
//               .map((e) => ListElement.fromJson(Map<String, dynamic>.from(e)))
//               .cast<ListElement>()
//               .toList();
//           return TeamAnalysisPage(
//             isLoading: false,
//             getCoursesResp: _getCoursesResp,
//             onMemberChanged: (selectedMemberId) {},
//           );
//         },
//       );
//     }
//     if (_selectedPage == 2) {
//       // return KPIWiseAnalysisPage();
//     }
//     // Fallback widget in case none of the above conditions are met
//     return SizedBox.shrink();
//   }

//   Widget _menuItems() {
//     double screenWidth = MediaQuery.of(context).size.width;
//     // double screenHeight = MediaQuery.of(context).size.height;
//     return SizedBox(
//       height: _selectedPage == 1
//           ? _selectedMemberId != null
//               ? 300
//               : 200
//           : 200,
//       child: Column(
//         children: [
//           Padding(
//             padding: EdgeInsets.only(left: 19),
//             child: SizedBox(
//               height: 120,
//               width: screenWidth,
//               child: ListView(
//                 shrinkWrap: true,
//                 scrollDirection: Axis.horizontal,
//                 children: [
//                   GestureDetector(
//                     onTap: () {
//                       Navigator.push(
//                           context,
//                           NextPageRoute(AskingRateCalculatorPage(
//                             isViewAll: true,
//                           )));
//                     },
//                     child: Container(
//                       height: 120,
//                       width: 200,
//                       padding: EdgeInsets.all(10),
//                       margin: EdgeInsets.only(right: 10),
//                       decoration: BoxDecoration(
//                         gradient: LinearGradient(colors: [
//                           Color(0xFF9DEAFB),
//                           Color(0xFFC6DFF5),
//                         ]),
//                         borderRadius: BorderRadius.all(Radius.circular(8)),
//                       ),
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Text(
//                             'Asking Rate Calculator',
//                             style: Styles.textBold(
//                               size: 16,
//                               color: context.appColors.textDarkBlack,
//                             ),
//                           ),
//                           SizedBox(
//                             height: 3,
//                           ),
//                           Padding(
//                             padding: EdgeInsets.only(right: 20.0),
//                             child: Text(
//                               '${Strings.of(context)?.Define_your_self}',
//                               style: Styles.textBold(
//                                 size: 12,
//                                 color: Color(0xCC000000),
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                   GestureDetector(
//                     onTap: () {
//                       Navigator.push(
//                           context,
//                           NextPageRoute(MyCertificatesPage(
//                             isViewAll: true,
//                           )));
//                     },
//                     child: Container(
//                       height: 120,
//                       width: 160,
//                       padding: EdgeInsets.all(10),
//                       margin: EdgeInsets.only(right: 10),
//                       decoration: BoxDecoration(
//                         gradient: LinearGradient(colors: [
//                           Color(0xFFFFEB3B),
//                           Color(0xFFFFD500),
//                         ]),
//                         borderRadius: BorderRadius.all(Radius.circular(8)),
//                       ),
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Text(
//                             '${Strings.of(context)?.Certification}',
//                             style: Styles.textBold(
//                               size: 16,
//                               color: context.appColors.textDarkBlack,
//                             ),
//                           ),
//                           Spacer(),
//                           Text(
//                             '${Strings.of(context)?.viewAll}',
//                             style: Styles.textBold(
//                               size: 16,
//                               color: Color(0xFF2E78E4),
//                             ),
//                           ),
//                           SizedBox(
//                             height: 3,
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//           Spacer(),
//           Padding(
//             padding: EdgeInsets.only(left: 19),
//             child: SizedBox(
//               height: 40,
//               width: screenWidth,
//               child: ListView.builder(
//                 shrinkWrap: true,
//                 scrollDirection: Axis.horizontal,
//                 itemCount: _pageList.length,
//                 controller: _pageListScrollController,
//                 itemBuilder: (ctx, index) {
//                   return GestureDetector(
//                     onTap: () {
//                       if (index == 0) {
//                         setState(() {
//                           _selectedPage = 0;

//                           _getCoursesListData(0);
//                         });
//                       }
//                       if (index == 1) {
//                         setState(() {
//                           _selectedPage = 1;

//                           _getCoursesListData(1);
//                         });
//                       }
//                       if (index == 2) {
//                         setState(() {
//                           _selectedPage = 2;
//                           _pageListScrollController.animateTo(
//                               _pageListScrollController
//                                   .position.maxScrollExtent,
//                               duration: Duration(milliseconds: 500),
//                               curve: Curves.easeOut);
//                         });
//                       }
//                     },
//                     child: Container(
//                       padding:
//                           EdgeInsets.symmetric(vertical: 6, horizontal: 12),
//                       margin: EdgeInsets.only(right: 16),
//                       alignment: Alignment.center,
//                       decoration: BoxDecoration(
//                           color: _selectedPage == index
//                               ? context.appColors.surface
//                               : Colors.transparent,
//                           borderRadius: BorderRadius.all(Radius.circular(8)),
//                           border: Border.all(
//                               color: context.appColors.surface, width: 2),
//                           boxShadow: [
//                             BoxShadow(
//                               blurRadius: _selectedPage == index ? 16 : 0,
//                               color: Color(0x0B000000),
//                             ),
//                           ]),
//                       child: Text(
//                         '${_pageList[index]}',
//                         style: _selectedPage == index
//                             ? Styles.textExtraBold(
//                                 size: 12,
//                                 color: Color(0xFF2E78E4),
//                               )
//                             : Styles.textSemiBold(
//                                 size: 12,
//                                 color: context.appColors.primaryForeground,
//                               ),
//                       ),
//                     ),
//                   );
//                 },
//               ),
//             ),
//           ),
//           SizedBox(
//             height: 7,
//           ),
//           Visibility(
//             visible: _selectedMemberId != null && _selectedPage == 1,
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.center,
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 SizedBox(
//                   height: 7,
//                 ),
//                 GestureDetector(
//                   onTap: () {
//                     setState(() {
//                       _selectedMemberId = null;
//                     });
//                   },
//                   child: Container(
//                     margin: EdgeInsets.symmetric(horizontal: 20),
//                     padding: EdgeInsets.symmetric(horizontal: 17, vertical: 11),
//                     width: screenWidth,
//                     height: 57,
//                     decoration: BoxDecoration(
//                       color: context.appColors.surface,
//                       borderRadius: BorderRadius.all(Radius.circular(10)),
//                       boxShadow: [
//                         BoxShadow(
//                           blurRadius: 16,
//                           color: Color(0x25000000),
//                         ),
//                       ],
//                     ),
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         SizedBox(
//                           width: screenWidth * 0.7,
//                           child: Row(
//                             children: [
//                               ClipRRect(
//                                 borderRadius:
//                                     BorderRadius.all(Radius.circular(15)),
//                                 child: SizedBox(
//                                   width: 30,
//                                   height: 30,
//                                   child: Image.network(
//                                     "https://images.pexels.com/photos/4462782/pexels-photo-4462782.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500",
//                                     fit: BoxFit.cover,
//                                   ),
//                                 ),
//                               ),
//                               SizedBox(
//                                 width: 10,
//                               ),
//                               SizedBox(
//                                 width: screenWidth * 0.5,
//                                 child: Column(
//                                   crossAxisAlignment: CrossAxisAlignment.start,
//                                   children: [
//                                     Text(
//                                       "Tanmay G Bansal",
//                                       style: Styles.textExtraBold(
//                                         size: 14,
//                                         color: Color(0xE41C2555),
//                                       ),
//                                       maxLines: 1,
//                                       overflow: TextOverflow.ellipsis,
//                                     ),
//                                     Text(
//                                       "Lead UI/UX designer",
//                                       style: Styles.textSemiBold(
//                                         size: 10,
//                                         color: Color(0x991C2555),
//                                       ),
//                                       maxLines: 1,
//                                       overflow: TextOverflow.ellipsis,
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ),
//                         Icon(
//                           Icons.keyboard_arrow_down_outlined,
//                           color: Color(0xCC1C2555),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//                 SizedBox(
//                   height: 7,
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   void _getCoursesListData(int type) {
//     Log.v("Loading....................GetCoursesState_getHomeData");
//     BlocProvider.of<HomeBloc>(context).add(GetCoursesEvent(type: type));
//   }

//   void _handleAnnouncmentData(GetCoursesState state) {
//     var loginState = state;

//     switch (loginState.apiState) {
//       case ApiStatus.LOADING:
//         _isLoading = true;
//         Log.v("Loading....................GetCoursesState");
//         break;
//       case ApiStatus.SUCCESS:
//         Log.v("Success....................GetCoursesState");
//         _isLoading = false;

//         break;
//       case ApiStatus.ERROR:
//         _isLoading = false;
//         Log.v("Error..........................GetCoursesState");
//         Log.v("Error..........................${loginState.error}");
//         break;
//       case ApiStatus.INITIAL:
//         break;
//     }
//   }
// }
